import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
export function set_permission (res) {
  // let permission = app.loginData.permission;
  let actions = app.loginData.user;
  // if (permission.all_action) {
  //   return res;
  // }
  let list = res;
  // list = list.filter((el) => {
  //   let find_item = actions.find((it) => {
  //     // return el.id * 1 === it.id * 1 || el.app === it.app;
  //     return el.app === it.app;
  //   });
  //   if (find_item) {
  //     el.acts = find_item.acts;
  //     return el;
  //   } else {
  //     return false;
  //   }
  // });
  console.log("处理后的list", list);
  return list;
}
export function handleRouter (data) {
  let color = [
    "rgba(0, 98, 255, 1.0)",
    "rgba(0, 157, 59, 1.0)",
    "rgba(255, 130, 0, 1.0)",
    "rgba(255, 146, 0, 1.0)",
    "rgba(255, 130, 0, 1.0)",
    "rgba(255, 146, 0, 1.0)",
  ];
  try {
    let routerlist;
    routerlist = set_permission(router_list);
    data.forEach((model) => {
      let data = model.data;
      if (data) {
        model.data = data.filter((route) => {
          return routerlist.find((local) => {
            if (route.app_type === "isv") {
              if (route.route === local.app) {
                if (!route.icon) {
                  try {
                    if (model.ctype.includes("app_group")) {
                      route.icon = `/images/index/${local.app}.png`;
                    }
                  } catch (e) {
                    route.icon = local.icon;
                  }
                }
                route.url = local.url;
                return true;
              }
            } else if (route.app_type === "diy") {
              if (!route.icon) {
                try {
                  if (model.ctype.includes("app_group")) {
                    route.icon = `/images/index/${local.app}.png`;
                  }
                } catch (e) {
                  route.icon = local.icon;
                }
              }
              route.url = route.route;
              return true;
            }
          });
        });
      }
    });
  } catch (e) {
    console.log(e);
  }
}
export var router_list = [
  {
    text: "测试",
    url: "/demo",
    icon: "datav",
    color: "rgba(0,186,70, 1.0)",
  },
  {
    text: "面包屑demo",
    url: "/bread",
    icon: "datav",
    color: "rgba(0,186,70, 1.0)",
  },
  {
    text: "表单demo",
    url: "/form",
    icon: "datav",
    color: "rgba(0,186,70, 1.0)",
  },
  {
    text: "分段器demo",
    url: "/steps",
    icon: "datav",
    color: "rgba(0,186,70, 1.0)",
  },
  {
    text: "骨架屏演示",
    url: "/skeleton-demo",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "菜品发布",
    url: "/dishRelease",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "档口管理",
    url: "/stall",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "餐时管理",
    url: "/menuTime",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "表格演示",
    url: "/table-demo",
    icon: "datav",
    color: "rgba(34,139,34, 1.0)",
  },
  {
    text: "联系人",
    url: "/contacts",
    icon: "datav",
    color: "rgba(0,123,255, 1.0)",
  },
  {
    text: "成本中心",
    url: "/costCenter",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "应用市场",
    url: "/app-market",
    icon: "datav",
    color: "rgba(138,43,226, 1.0)",
  },
  {
    text: "餐时组",
    url: "/menuTimeGroup",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "餐费设置",
    url: "/menuMeal",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "消费规则",
    url: "/consumeRule",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "菜品分类",
    url: "/dishSort",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "菜品配置",
    url: "/dishConfig",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "包间配置",
    url: "/roomConfig",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "个性化空间",
    url: "/personalSpace",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "审计日志",
    url: "/auditLog",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "系统公告",
    url: "/announcement",
    text: "公费预定",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  //财务管理的
  {
    text: "交易流水",
    url: "/finance/runningTab",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  // 餐厅管理的
  {
    text: "公费预定",
    url: "/publicRese",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "包间预定",
    url: "/reserve/roomReserve",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "公费预定",
    url: "/reserve/publicReserve",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "系统公告",
    url: "/announcement",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "审计日志",
    url: "/auditLog",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "计划任务",
    url: "/scheduledTask",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "减免规则",
    url: "/systemConfig/reduceRule",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "企业文化与驾驶舱",
    url: "/systemConfig/corporateCulture",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "场所管理",
    url: "/venueSetup",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "基础配置",
    url: "/systemConfig/basicConfiguration",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "设备管理",
    url: "/systemConfig/deviceManagement",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "考勤规则",
    url: "/systemConfig/attendanceRule",
    text: "场所管理",
    // url: "/venueSetup",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "发放补贴",
    url: "/provideSubsidies",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "扣除补贴",
    url: "/deductSubsidies",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
  {
    text: "清空欠款",
    url: "/clearDebt",
    icon: "datav",
    color: "rgba(255,140,0, 1.0)",
  },
];
export let routerList = set_permission(router_list);
