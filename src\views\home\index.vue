<template>
  <div class="home-container">
    <div class="welcome-section">
      <van-icon name="home-o" size="48" color="#1989fa" />
      <h2>欢迎使用云一消费系统</h2>
    </div>
  </div>
</template>
<script setup>
// 简化的首页组件
// 可在此处添加新的业务逻辑
</script>
<style lang="scss" scoped>
.home-container {
  padding: 20px;
  min-height: 100vh;
  background: #f7f8fa;
}
.welcome-section {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 16px 0 8px;
    color: #323233;
    font-size: 20px;
    font-weight: 500;
  }

  p {
    color: #969799;
    font-size: 14px;
    margin: 0;
  }
}

.content-section {
  background: white;
  border-radius: 8px;
  padding: 40px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
