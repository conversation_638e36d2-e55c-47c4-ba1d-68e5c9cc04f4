<template>
  <div class="result-container">
    <!-- 蓝色背景区域 -->
    <div class="blue-section">
      <!-- 成功图标 -->
      <div class="success-icon">
        <div class="check-mark">✓</div>
      </div>

      <!-- 标题 -->
      <div class="result-title">补贴发放结果</div>
    </div>

    <!-- 白色背景区域 -->
    <div class="white-section">
      <!-- 白色卡片 -->
      <div class="detail-card">
        <div class="card-title">发放补贴明细</div>

        <!-- 结果列表 -->
        <div class="result-list">
          <div class="list-header">
            <span class="header-name">人员</span>
            <span class="header-result">结果</span>
          </div>

          <template v-for="(item, index) in resultList" :key="index">
            <div class="result-item">
              <span class="item-name">{{ item.user_name }}</span>
              <span
                :class="['item-result', item.success ? 'success' : 'failed']"
              >
                {{ 
                  item.message
                }}
              </span>
            </div>
          </template>
        </div>

        <!-- 展开按钮 -->
        <div class="expand-btn" @click="toggleExpand" v-if="hasMoreItems">
          <van-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" />
          <span>{{ isExpanded ? '收起' : '展开' }}</span>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="back-btn-container">
        <van-button
          type="primary"
          size="large"
          @click="goBack"
          class="back-btn"
        >
          返回上一级
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useLoginStore } from '@/store/dingLogin'

const router = useRouter()
const route = useRoute()
const app = useLoginStore()

// 响应式数据
const isExpanded = ref(false)
const allResults = ref([])

// 模拟结果数据（实际应该从路由参数或API获取）
const mockResults = [
  { name: '徐星辰', success: true, errorMsg: '' },
  { name: '蔚玥', success: true, errorMsg: '' },
  { name: '曼特宁', success: false, errorMsg: '发放失败，用户不存在' },
  { name: '于晓东', success: false, errorMsg: '发放失败，用户不存在' },
  { name: '白潇潇', success: false, errorMsg: '发放失败，用户不存在' },
  { name: '史欣汝', success: false, errorMsg: '发放失败，用户不存在' }
]



// 计算属性
const resultList=ref([
  {
    user_id: "171147035138649938",
    user_name: "马俊杰",
    success: true,
    message: "发放成功，余额: 743.95"
  }
])
const hasMoreItems = computed(() => resultList.value.length > 3)
//const hasMoreItems = computed(() => allResults.value.length > 3)
// const resultList = computed(() => {
//   if (isExpanded.value || allResults.value.length <= 3) {
//     return allResults.value
//   }
//   return allResults.value.slice(0, 3)
// })

// 页面加载
onMounted(() => {
  // 从路由参数获取结果数据，如果没有则使用模拟数据
  if (route.query.results) {
    try {
      allResults.value = JSON.parse(route.query.results)
    } catch (error) {
      console.error('解析结果数据失败:', error)
      allResults.value = mockResults
    }
  } else {
    allResults.value = mockResults
  }
})

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 返回上一页
const goBack = () => {
  // 清空发放补贴的选中状态
  app.provideSubsidies.externalUserList = []
  app.provideSubsidies.visiterList = []

  // 返回上一页
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.result-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.blue-section {
  background: #007FFF;
  height: 200px;
  padding: 40px 20px 20px 20px;
  position: relative;
  border-radius: 0 0 25px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .check-mark {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 28px;
      font-weight: bold;
      line-height: 1;
    }

    .van-icon {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #007FFF;
      font-size: 24px;
      font-weight: bold;
    }
  }

  .result-title {
    text-align: center;
    color: #fff;
    font-size: 15px;
    font-weight: 500;
  }
}

.white-section {
  flex: 1;
  background: #f5f5f5;
  padding: 0 16px;
  // margin-top: -8px;
  // position: relative;
  z-index: 2;
}

.detail-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-top: -15px;
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
  }
}



.result-list {
  .list-header {
    width: 100%;
    // display: flex;
    // justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 8px;
    
    .header-name{
      font-size: 15px;
      color: #969799;
      width:30%;
      font-weight: 500;
    }
    .header-result {
      font-size: 15px;
      color: #000000;
      margin-left: 23%;
      width:30%;
      font-weight: 500;
    }
  }
  
  .result-item {
    // display: flex;
    // justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-name {
      font-size: 15px;
      color: #969799;
    }
    
    .item-result {
      font-size: 15px;
      margin-left: 23%;
      &.success {
        color: #07c160;
      }
      
      &.failed {
        color: #ED6A0C;
      }
    }
  }
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  color: #007FFF;
  font-size: 15px;
  cursor: pointer;

  .van-icon {
    width: 20px;
    height: 20px;
    background: #007FFF;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
  }
}

.back-btn-container {
  margin-top: 30px;
  padding-bottom: 30px;

  .back-btn {
    width: 200px;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
    display: block;
  }
}
</style>
