<template>
  <div class="group-detail-container">
    <!-- 页面标题 -->
    <van-nav-bar
      title='设备组详情'
    >
      <template #right>
        <span class="edit-button" @click="handleEdit">编辑</span>
      </template>
    </van-nav-bar>

    <!-- 搜索框 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索设备名称"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 组名显示 -->
    <div class="group-name-section">
      <h3>{{ groupInfo?.name }}</h3>
      <!-- <span class="edit-link" @click="goToEditGroup">编辑</span> -->
    </div>

    <!-- 筛选和设备列表 -->
    <div class="content-card">
      <!-- 排序选择 -->
      <div class="sort-section">
        <div class="sort-item" @click="showSortOptions">
          <span>按设备名称排序 <img src="/svg/sanjiao.svg" alt="排序" class="sort-icon" /></span>
        </div>
      </div>

      <!-- 设备列表 -->
      <div class="device-list">
        <div
          v-for="device in filteredDevices"
          :key="device.id"
          class="device-item"
          @click="goToDeviceDetail(device.id)"
        >
          <div class="device-avatar">
            <van-image :src="device.avatar || '/images/device-default.png'" width="48" height="48" />
          </div>
          <div class="device-info">
            <div class="device-name">{{ device.name }}</div>
            <div class="device-details">
              <div class="device-sn">SN: {{ device.sn }}</div>
            </div>
          </div>
          <div class="device-status" :class="{ online: device.status === 'online' }">
            {{ device.status === 'online' ? '在线' : '离线' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 排序选项弹窗 -->
    <van-action-sheet
      v-model:show="showSortSheet"
      :actions="sortActions"
      @select="onSortSelect"
      cancel-text="取消"
      title="排序规则"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'

// 路由实例
const route = useRoute()
const router = useRouter()

// 页面数据
const groupInfo = ref(null)
const searchKeyword = ref('')
const showSortSheet = ref(false)
const currentSort = ref('name')

// 设备列表
const devices = ref([
  {
    id: 1,
    name: '设备名称',
    sn: '1234567851',
    model: 'D3-KN1661',
    status: 'online',
    avatar: '/images/consumer-device.png'
  },
  {
    id: 2,
    name: '设备名称',
    sn: 'CM001234567891',
    model: 'D3-KN1661',
    status: 'offline',
    avatar: '/images/consumer-device.png'
  },
  {
    id: 3,
    name: '设备名称',
    sn: 'CM001234567892',
    model: 'D3-KN1661',
    status: 'online',
    avatar: '/images/consumer-device.png'
  }
])

// 排序选项
const sortActions = [
  {
    name: '按设备名称排序',
    value: 'name'
  },
  {
    name: '按激活时间正序',
    value: 'activeTimeAsc'
  },
  {
    name: '按激活时间倒序',
    value: 'activeTimeDesc'
  }
]

// 过滤后的设备列表
const filteredDevices = computed(() => {
  let result = [...devices.value]
  
  // 搜索过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    result = result.filter(device =>
      device.name.toLowerCase().includes(keyword) ||
      device.sn.toLowerCase().includes(keyword)
    )
  }
  
  // 排序
  switch (currentSort.value) {
    case 'name':
      result.sort((a, b) => a.name.localeCompare(b.name))
      break
    case 'activeTimeAsc':
      // 这里可以根据实际的激活时间字段排序
      break
    case 'activeTimeDesc':
      // 这里可以根据实际的激活时间字段排序
      break
  }
  
  return result
})

// 搜索功能
const onSearch = () => {
  console.log('搜索:', searchKeyword.value)
}

const onClear = () => {
  searchKeyword.value = ''
}

// 显示排序选项
const showSortOptions = () => {
  showSortSheet.value = true
}

// 排序选择
const onSortSelect = (action) => {
  currentSort.value = action.value
  showSortSheet.value = false
  showToast(`已切换为${action.name}`)
}

// 跳转到设备详情
const goToDeviceDetail = (deviceId) => {
  router.push({
    path: `/app/systemConfig/deviceManagement/detail`,
    query: {
      type: route.query.type,
      id: deviceId,
      groupId: route.query.groupId
    }
  })
}

// 跳转到编辑设备组
const goToEditGroup = () => {
  router.push({
    path: `/app/systemConfig/deviceManagement/editGroup`,
    query: {
      type: route.query.type,
      groupId: route.query.groupId
    }
  })
}

// 处理编辑按钮点击
const handleEdit = () => {
  goToEditGroup()
}

// 页面初始化
onMounted(() => {
  const groupId = route.query.groupId
  const deviceType = route.query.type
  
  // 模拟获取组信息
  groupInfo.value = {
    id: groupId,
    name: 'D3组',
    type: deviceType
  }
})
</script>

<style lang="scss" scoped>
.group-detail-container {
  min-height: 100vh;
  background: #f7f8fa;
}

// 搜索区域
.search-section {
  background: white;
  height: 52px;
  padding: 8px 16px;
  display: flex;
  align-items: center;

  :deep(.van-search) {
    flex: 1;
    padding: 0;
    background: transparent;

    .van-search__content {
      height: 36px;
      background: #f7f8fa;
      border-radius: 18px;
      border: none;
    }

    .van-field__control {
      height: 36px;
      line-height: 36px;
    }
  }
}

// 组名区域
.group-name-section {
  //background: white;
  padding: 16px ;
  // margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #323233;
    font-weight: 600;
  }

  .edit-link {
    color: #007fff;
    font-size: 14px;
    cursor: pointer;
    padding: 4px 8px;

    &:active {
      opacity: 0.7;
    }
  }
}

// 内容卡片容器
.content-card {
  background: white;
  margin:0 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 排序区域
.sort-section {
  padding: 16px;

  .sort-item {
    cursor: pointer;

    span {
      font-size: 14px;
      color: #323233;
      line-height: 1.4;
      display: flex;
      align-items: center;
      gap: 4px;

      .sort-icon {
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }
  }
}

// 修改分组按钮区域
.edit-group-section {
  background: white;
  padding: 16px;
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;

  .edit-group-button {
    min-width: 120px;
    height: 32px;
    border-radius: 16px;
    font-size: 14px;

    &:active {
      opacity: 0.8;
    }
  }
}

// 设备列表
.device-list {
  padding: 0;

  .device-item {
    display: flex;
    align-items: center;
    padding: 16px;
    height: 89px;
    cursor: pointer;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 76px; // 16px padding + 48px icon + 12px margin
      right: 0;
      bottom: 0;
      height: 1px;
      background: #f7f8fa;
    }

    &:active {
      background: #f7f8fa;
    }

    .device-avatar {
      margin-right: 12px;

      .van-image {
        border-radius: 8px;
      }
    }

    .device-info {
      flex: 1;

      .device-name {
        font-size: 16px;
        color: #171A1D;
        font-weight: 500;
        margin-bottom: 16px;
      }

      .device-details {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .device-sn {
          font-size: 14px;
          color: #969799;
        }
      }
    }

    .device-status {
      font-size: 12px;
      color: #ee0a24;
      padding: 4px 8px;
      border-radius: 4px;
      background: rgba(238, 10, 36, 0.1);

      &.online {
        color: #07c160;
        background: rgba(7, 193, 96, 0.1);
      }
    }
  }

  // 编辑按钮样式
  .edit-button {
    font-size: 14px;
    color: #1989fa;
    padding: 4px 8px;
    cursor: pointer;

    &:active {
      opacity: 0.8;
    }
  }
}

// 移除导航栏边框
:deep(.van-nav-bar) {
  border-bottom: none;

  &::after {
    display: none;
  }
}
</style>
