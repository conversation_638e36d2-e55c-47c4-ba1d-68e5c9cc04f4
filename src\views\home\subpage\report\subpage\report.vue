<template>
  <div class="wraper-order-confirm">
    <div class="top-bg"></div>
    <div class="address-info top" v-if="item.advanced&&typeListLength!=1">
      <div class="address-title">
        <van-cell :title="app.loginData.dininghall_title" :border="false" />
        <!-- <div class="type">
          <div :class="`${data.type === 0 && 'active'}`" @click="data.type = 0">
            自提
          </div>
          <div :class="`${data.type === 1 && 'active'}`" @click="data.type = 1">
            外卖
          </div>
        </div> -->
      </div>
      <div class="time-bottom address-title">
        <div class="title" style="width: 100px">
          {{ app.item.window_title }}
        </div>
        <van-cell :title="data.date" :border="false" />
      </div>
      <div class="meal-type-block">
        <div
          :class="{
            'meal-type': true,
            'meal-type3': typeListLength < 3,
            active: mealType === i,
          }"
          v-for="(item, i) in typeList"
          :key="item.en"
          @click="onMealTypeClick(i)"
          v-show="item.isShow * 1"
        >
          <van-image
            style="background-color: #fff; padding: 1px"
            round
            :width="typeListLength < 3 ? '38px' : '28px'"
            :height="typeListLength < 3 ? '38px' : '28px'"
            :src="item.icon"
          />
          <div class="meal-type-info">
            <div style="font-size: 12px; font-weight: 500; margin-bottom: 2px">
              {{ item.title }}
            </div>
            <div style="transform: scale(0.9)">{{ item.en }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="address-info dish-list-block" v-if="item.multi || item.dept">
      <div class="dish-title">
        <div class="tip">
          报餐方式
          <van-tag
            style="margin-left: 5px; transform: scale(0.8)"
            plain
            type="warning"
            >必填</van-tag
          >
        </div>
      </div>
      <van-field
        v-model="reportType"
        is-link
        readonly
        label="报餐方式"
        placeholder="选择报餐方式"
        @click="showReportType = true"
      />
      <van-popup v-model:show="showReportType" round position="bottom">
        <van-picker
          :columns="reportList"
          @cancel="showReportType = false"
          @confirm="onConfirm"
        />
      </van-popup>
      <van-field
        v-if="reportTypeVal == '1'"
        v-model="count"
        type="number"
        label="份数"
        is-link
        placeholder="请输入份数"
        ref="inputRef"
            @click="focus('inputRef')"
      />
      <van-field
        v-if="reportTypeVal == 'dept'"
        v-model="data.users"
        type="text"
        label="人员"
        readonly
        is-link
        placeholder="请选择部门人员"
        @click="onSelectDept"
      />
    </div>

    <div class="address-info dish-list-block">
      <div class="dish-title">
        <div class="tip">订单信息</div>
      </div>
      <div :class="`dish-list ${data.isShowAll && 'show-all'}`">
        <div class="dish-item" v-for="(item, i) in data.selectList" :key="i">
          <div class="left">
            <van-image
              v-if="item.image"
              width="56"
              height="56"
              radius="4"
              :src="
                item.image ||
                'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
              "
            />
            <div
              v-else
              style="
                width: 56px;
                height: 56px;
                border-radius: 4px;
                background: #007fff;
                color: #fff;
                font-size: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              {{ item.title[0] }}
            </div>
          </div>
          <div class="right">
            <div>{{ item.title }}</div>
            <div class="dish-info">
              <div>{{ item.desc }}</div>
              <div>X{{ item.count || 1 }}</div>
              <div>￥{{ item.price * 1 * (item.count || 1) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div style="text-align: center">
        <div class="show-all-button" @click="data.isShowAll = !data.isShowAll">
          <van-cell
            :title="data.isShowAll ? '收起' : '展示全部'"
            is-link
            :arrow-direction="data.isShowAll ? 'up' : 'down'"
          />
        </div>
      </div>

      <div class="detail-money"></div>
    </div>
    <div class="address-info" style="margin-top: 16px">
      <van-field
        v-model="data.desc"
        rows="2"
        autosize
        label="备注"
        type="textarea"
        maxlength="50"
        placeholder="口味、偏好等"
        show-word-limit
        :disabled="!!data.orderId"
      />
    </div>
    <div class="goods-bar" v-if="!data.orderId">
      <div class="total">
        <span style="margin-left: 16px; font-size: 14px">总价</span>
        <span style="font-size: 17px"
          >￥{{ app.item.price * (count || 1) }}</span
        >
      </div>
      <van-button round type="primary" @click="onSettlementClick"
        >去结算</van-button
      >
    </div>
  </div>
</template>

<script setup>
import {
  onUnmounted,
  onActivated,
  ref,
  reactive,
  getCurrentInstance,
  computed,
} from "vue";
import { showToast, closeToast, showLoadingToast } from "vant";
import dayjs from "dayjs";

import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
let data = reactive({
  orderId: "",
  desc: "",
  type: 0,
  isShowAll: false,
  date: dayjs().add(20, "minute").format("MM月DD日(今日)HH:mm"),
  selectList: new Set(app.item.dishess),
  users: "",
});

let typeList = [
  {
    title: "堂食",
    en: "Dine in",
    icon: "/images/common/dine.png",
    isShow: 1,
  },
  {
    title: "留餐",
    en: "Eat later",
    icon: "/images/common/eat.png",
    isShow: app.item.keep,
  },
  {
    title: "打包",
    en: "Take away",
    icon: "/images/common/take.png",
    isShow: app.item.bale,
  },
];
let typeListLength = typeList.filter((el) => el.isShow == 1).length;
let mealType = ref(0);
let reportType = ref("");
let reportTypeVal = ref("0");
let count = ref();
let showReportType = ref(false);
const reportList = [];
let item = app.item;
if (item.multi) {
  reportList.push({ text: "多人报餐", value: "1" });
}
if (item.dept) {
  reportList.push({ text: "部门报餐", value: "dept" });
}
if (reportList[0]) {
  reportType.value = reportList[0].text;
  reportTypeVal.value = reportList[0].value;
}
let inputRef = ref();
let count1 = 1;
const focus = (key) => {
  console.log([key].value);
  let input = [key].value.$el.querySelector("input");
  if (input && count1) {
    input.focus();
    let length = input.value.length;
    // input.setSelectionRange(length, length);
    input.selectionStart = length;
    input.selectionEnd = length;
    --count1;
  }
};
onActivated(() => {
  if (app.deptList && app.deptList.length) {
    data.users = app.deptList.map((el) => el.name).join(",");
  } else {
    data.users = "";
  }
});
onUnmounted(() => {
  delete app.deptList;
  delete app.dept;
});
const onSelectDept = () => {
  router.push({
    path: "/selectDept",
  });
};
const onMealTypeClick = (i) => {
  mealType.value = i;
};
const onConfirm = (e) => {
  reportType.value = e.selectedOptions[0].text;
  reportTypeVal.value = e.selectedOptions[0].value;
  showReportType.value = false;
};

const onSettlementClick = () => {
  let postData = {
    repast_id: item.repast_id,
    window_id: item.window_id,
    repast_title: item.repast_title,
    window_title: item.window_title,
    date: item.date,
    desc: data.desc,
    dine_type: mealType.value,
  };

  if (reportTypeVal.value == "1") {
    if (count.value === undefined) {
      showToast("请输入报餐份数");
      return;
    }
    postData.count = count.value;
  }
  if (reportTypeVal.value == "dept") {
    if (!app.dept || (app.deptList && !app.deptList.length)) {
      showToast("请选择部门人员");
      return;
    }
    postData.user_data = app.deptList.map((user) => ({
      userid: user.userid,
      name: user.name,
    }));
    postData.dept_id = app.dept.dept_id;
    postData.dept_name = app.dept.name;
  }
  showLoadingToast({
    message: "报餐中...",
    forbidClick: true,
  });
  proxy
    .$post("apply/post_apply_add", postData)
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        data.orderId = "ok";
        showToast("报餐成功~");
        if (res.bill_id) {
          setTimeout(() => {
            closeToast();
            router.back();
          }, 1500);
        } else {
          closeToast();
          router.push({
            path: "/reportResult",
            query: {
              res:JSON.stringify(res)
            },
          });
        }
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
      setTimeout(() => {
        closeToast();
      }, 3000);
    });
};
</script>

<style lang="scss">
.wraper-order-confirm {
  padding-bottom: 120px;
  .top-bg {
    background: linear-gradient(#007fff, #e4edf6);
    height: 250px;
    margin-bottom: -243px;
  }

  .address-info {
    margin: 0 16px;
    border-radius: 8px;
    background: #ffffff;
    overflow: hidden;
    margin-top: 16px;
    .address-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .van-cell__title {
        font-size: 15px;
      }
      .type {
        width: 150px;
        font-size: 14px;
        background: rgba(0, 0, 0, 0.16);
        display: flex;
        justify-content: space-between;
        padding: 1px;
        margin-right: 16px;
        border-radius: 4px;
        div {
          padding: 5px 8px;
          border-radius: 4px;
        }
      }
      .active {
        background: #fff;
      }
    }
    .time-bottom {
      .title {
        font-size: 14px;
        margin-left: 16px;
      }
      .van-cell__title {
        text-align: right;
        font-size: 14px;
        color: #007fff !important;
      }
    }
  }
  .top {
    .meal-type-block {
      display: flex;
      justify-content: space-between;
      padding: 12px;
      .meal-type {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #007fff;
        padding: 8px 0;
        width: 30%;
        border-radius: 8px;
        color: #007fff;
        font-size: 12px;
        .meal-type-info {
          margin-left: 8px;
          text-align: center;
        }
      }
      .meal-type3 {
        width: 48%;
        height: 66px;
      }
      .active {
        background: #007fff;
        color: #ffffff;
      }
    }
  }
  .dish-list-block {
    // margin-top: 16px;

    .dish-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      .tip {
        display: flex;
        align-items: center;
      }
    }
    .dish-list {
      max-height: 300px;
      .dish-item {
        padding: 12px;
        display: flex;
        .right {
          flex: 1;
          font-size: 14px;
          margin-left: 10px;
          .dish-info {
            margin-top: 4px;
            display: flex;
            justify-content: space-between;
            color: rgba(23, 26, 29, 0.4);
            font-size: 10px;
            div:nth-of-type(1) {
              width: 100px;
            }
            div:nth-of-type(3) {
              font-size: 14px;
              color: #171a1d;
            }
          }
        }
      }
    }
    .show-all {
      height: auto;
      max-height: none;
    }
    .show-all-button {
      background: #f2f2f6;
      border-radius: 50px;
      width: 90px;
      overflow: hidden;
      display: inline-block;
      .van-cell {
        background: #f2f2f6;
        padding: 2px 8px;
        color: rgba(23, 26, 29, 0.4);
        font-size: 10px;
      }
    }
    .detail-money {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      margin: 12px;
      .title {
      }
      .money {
        color: #ff5219;
      }
    }
    .van-field__label {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.66);
    }
    .van-field__control {
      text-align: right;
      color: #007fff;
    }
  }
}
.goods-bar {
  padding: 0 12px;
  position: fixed;
  bottom: 16px;
  left: 0;
  right: 0;
  height: 58px;
  border-radius: 29px;
  opacity: 1;
  background: #ffffff;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px;
  .total {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
