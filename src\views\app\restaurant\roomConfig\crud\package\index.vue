<template>
  <div class="skeleton-demo-container">
    <!-- 列表组件演示 -->
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
      <!-- 新增按钮插槽 -->
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增套餐</span>
          </div>
        </div>
      </template>

      <template #default="{ item }">
        <div class="demo-item" @click.stop="onCardClick(item)">
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-time">{{item.dishes ? JSON.parse(item.dishes).map(i => i.title).join(' + ') : ''}}</div>
            <div class="frame">
              <div class="text-">
                ¥
              </div>
              <div class="text-376">
                {{ item.dishes?JSON.parse(item.dishes).reduce((a, b) => a + b.price, 0) : 0 }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { showToast } from 'vant'
import { useLoginStore } from "@/store/dingLogin";
const router = useRouter();
const applist = useLoginStore();
applist.title = null;
applist.dishFoodList = [];
// 骨架屏配置
const skeletonConfig = reactive({
  isShow: false,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
// 列表组件引用
const listRef = ref(null)

// 列表配置
const listConfig = reactive({
  curl: {
    ls: '/meal_set/get_ls' // 留空，使用模拟数据
  },
  postData: {
    dininghall_id: localStorage.getItem('dininghall')
  },
  search: {
    isShow: true,
    isShowPopup: false,
    key: 'title' // 搜索字段
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    // 这里可以对数据进行格式化处理
    console.log('格式化数据:', data)
  },
  // 添加模拟数据标识
  mockData: true
})


// 新增按钮点击事件
const onAddClick = () => {
  //   router.push('/roomConfig/package/add')
  // 清空菜品列表，初始化为空数组
  applist.dishFoodList = [];
  applist.title = "";
  router.push({
    path: "/form_selectDishList",
    query: { url: "/form_dishes_publicAdd" },
  });
}
const onCardClick = (item) => {
  router.push({ path: "/form_dishes_publicdetail", query: { id: item.id } });
};
</script>

<style lang="scss" scoped>
.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;

  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;

    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 16px;
  background: #fff;
  border-radius: 8px;

  .item-content {
    flex: 1;
    min-width: 0;
    .frame {
    // width: 191px;
    // height: 21px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    column-gap: 2px; 
    row-gap: 0px;
    .text- {
        flex-shrink: 0;
        width: 9px;
        height: 20px;
        white-space: nowrap;
        color: #ED6A0C;
        font-family: "PingFang SC";
        font-size: 14px;
        line-height: 20px;
    }
    .text-376 {
        flex-shrink: 0;
        width: 180px;
        height: 21px;
        color: #ED6A0C;
        font-family: "PingFang SC";
        font-size: 16px;
        line-height: 21px;
    }
}

    /* 确保flex子项能够收缩 */
    .item-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: normal;
      color: #171A1D;
      margin-bottom: 8px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
