<template>
  <div class="add-department-page">
    <!-- 表单内容 -->
    <div class="form-container">
      <div class="group-wrapper">
        <van-field v-model="departmentName" label="部门名称" :placeholder="placeholder" class="form-field" />
      </div>
    </div>

    <!-- 底部完成按钮 -->
    <div class="bottom-action">
      <van-button type="primary" size="large" class="complete-btn" @click="handleComplete"
        :disabled="!departmentName.trim()">
        完成
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const departmentName = ref('')

// 计算页面标题和占位符
const isExternal = computed(() => route.query.type === 'external')
const pageTitle = computed(() => isExternal.value ? '添加外部部门' : '添加子部门')
const placeholder = computed(() => isExternal.value ? '请输入外部部门名称' : '请输入子部门名称')

// 完成添加
const handleComplete = () => {
  if (!departmentName.value.trim()) {
    showToast({
      message: '请输入部门名称',
      type: 'fail'
    })
    return
  }

  // 获取父部门信息和类型
  const parentDeptName = route.query.parentName || '未知部门'
  const isExternal = route.query.type === 'external'

  console.log(isExternal ? '添加外部部门:' : '添加子部门:', {
    name: departmentName.value.trim(),
    parentDept: parentDeptName,
    parentId: route.query.parentId,
    type: isExternal ? 'external' : 'sub'
  })

  showSuccessToast(isExternal ? '外部部门添加成功' : '子部门添加成功')

  // 延迟返回上一页
  setTimeout(() => {
    router.back()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.add-department-page {
  min-height: 100vh;
  background: #f2f3f4;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部固定按钮留出空间
}

.form-container {
  flex: 1;
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .form-field {
      font-size: 16px;
      padding: 16px;

      :deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
      }

      :deep(.van-field__control) {
        color: #323233;

        &::placeholder {
          color: #c8c9cc;
        }
      }
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .complete-btn {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:disabled {
      background: #c8c9cc;
      border-color: #c8c9cc;
    }
  }
}
</style>
