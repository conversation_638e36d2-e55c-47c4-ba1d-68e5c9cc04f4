<template>
  <div class="member-restore-page">
    <!-- 表单内容 -->
    <div class="form-container">
      <div class="group-wrapper">
        <van-field v-model="memberName" label="姓名" class="form-field" readonly />
        <van-field v-model="memberPhone" label="手机号" class="form-field" readonly />
      </div>
    </div>

    <!-- 底部恢复按钮 -->
    <div class="bottom-action">
      <van-button type="default" size="large" class="restore-btn" @click="handleRestore">
        恢复
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showSuccessToast } from 'vant'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const memberName = ref('')
const memberPhone = ref('')

// 恢复人员
const handleRestore = () => {
  console.log('恢复人员:', {
    id: route.query.id,
    name: memberName.value,
    phone: memberPhone.value
  })

  showSuccessToast('人员恢复成功')
  
  // 延迟返回上一页
  setTimeout(() => {
    router.back()
  }, 1500)
}

// 加载人员数据
const loadMemberData = () => {
  // 从路由参数获取人员信息
  memberName.value = route.query.name || '徐星辰'
  memberPhone.value = route.query.phone || '15003903233'
}

// 页面挂载时加载数据
onMounted(() => {
  loadMemberData()
})
</script>

<style lang="scss" scoped>
.member-restore-page {
  min-height: 100vh;
  background: #f2f3f4;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部固定按钮留出空间
}

.form-container {
  flex: 1;
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .form-field {
      font-size: 16px;
      padding: 16px;
      
      :deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
      }
      
      :deep(.van-field__control) {
        color: #323233;
        
        &[readonly] {
          background: transparent;
          color: #323233;
        }
      }
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .restore-btn {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    background: transparent;
    border: 1px solid #e0e2e4;
    color: #323233;
    
    &:hover {
      background: #f7f8fa;
    }
  }
}
</style>
