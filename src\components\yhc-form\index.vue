<template>
  <div class="wrapper-form" v-if="isShow">
    <van-form @submit="onSubmit">
      <div :class="config.form.slice(...arr)[0]?.component === 'yhc-desc' ? 'desc-wrapper' : 'group-wrapper'"
        v-for="(arr, index) in config.groupForm" :key="index">
        <yhc-form-item v-for="(item, i) in config.form.slice(...arr)" :key="item.key" :config="item" :form="form" />
      </div>
      <div v-if="config.button.isShow" style="z-index: 0;"
        :class="`${config.button.position == 'bottom' ? 'button-pos' : 'button-normal'}`">
        <!-- detail页面：显示删除和修改两个按钮 -->
        <div v-if="buttonConfig.showDoubleButtons" class="button-group">
          <van-button :loading="deleteLoading" :type="'default'" :loading-text="'正在删除..'" :plain="true" hairline
            class="delete-button" @click="onDelete" round >
            删除
          </van-button>
          <van-button :loading="false" :type="config.button.type" :plain="config.button.plain" hairline
            class="edit-button" @click="onEdit" round >
            修改
          </van-button>
        </div>
        <!-- add页面：显示单个按钮（保存/提交） -->
        <van-button v-else-if="buttonConfig.showSingleButton" :loading="loading" block :type="config.button.type"
          :loading-text="`正在${buttonConfig.singleButtonText}..`" native-type="submit" :plain="config.button.plain"
          hairline round >
          {{ buttonConfig.singleButtonText }}
        </van-button>
      </div>
    </van-form>
  </div>
  <van-empty v-else description="加载中.." />
</template>
<script setup>
import {
  ref,
  reactive,
  computed,
  getCurrentInstance,
  watchEffect,
  watch,
  toRaw,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { deepAssign, handleJson, handleFormData } from "@/untils";
import { showToast, showConfirmDialog } from "vant";
import { useLoginStore } from "@/store/dingLogin";
import { useRoute, useRouter } from "vue-router";

const app = useLoginStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  config: Object,
  form: Object,
  // 页面类型：'add' 或 'detail'
  pageType: {
    type: String,
    default: 'add',
    validator: (value) => ['add', 'detail'].includes(value)
  },
  // 修改按钮跳转配置（仅在detail页面使用）
  editRedirectConfig: {
    type: Object,
    default: () => ({})
  },
  // 是否启用自动缓存功能
  enableAutoCache: {
    type: Boolean,
    default: true
  }
});
let isShow = ref(false);
let form = reactive({
  // "yhc-switch": 1,
  // "yhc-calendar": '["2023-6-19", "2023-6-22"]',
  // "yhc-input": "dfgdfgdfg",
  // "yhc-picker": '[{"id":96},{"id":93}]',
  // "yhc-select-image":
  //   '["http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2023-06-18/5SDsgJOTp0J1s3CuSrGPA9gSFXrum2dc.jpg"]',
  // "yhc-select-user":
  //   '{"departments":[{"dept_id":"278542050","name":"部门2"},{"dept_id":"299939017","name":"软件事业部"}],"users":[{"userid":"135933524724483168","name":"张菲菲","avatar":"https://static.dingtalk.com/media/lADPD3zUS-US2vPNAa_NAa8_431_431.jpg_60x60q90.jpg"},{"userid":"2865524504-1710204866","name":"王佳文","avatar":"https://static.dingtalk.com/media/lQLPM4xgmIY6XUjNArLNArKwRJAj3-OXCfAEEleESYCFAA_690_690.png_60x60q90.png"}]}',
});
handleJson(form, false);
let config = {
  // API接口配置
  curl: {
    add: "",           // 新增数据接口地址 (字符串) - 例: "/api/add" - 表单提交时调用
    edit: "",          // 编辑数据接口地址 (字符串) - 例: "/api/edit" - 修改数据时调用
    info: "",          // 获取详情接口地址 (字符串) - 例: "/api/info" - 编辑模式时获取数据
    del: "",        // 删除数据接口地址 (字符串) - 例: "/api/delete" - 删除操作时调用
  },
  postData: {},        // 请求参数 (对象) - 发送给接口的额外参数，如: {id: 1, category_id: 2}

  // 表单字段配置
  form: [],           // 表单字段数组 (数组) - 表单项配置，每个元素为一个字段配置对象
  groupForm: [],      // 表单分组配置 (数组) - 控制表单字段的分组显示，如: [[0,2], [2,4]]

  // 按钮配置
  button: {
    isShow: true,      // 是否显示按钮 (布尔值) - true: 显示提交按钮, false: 隐藏按钮
    text: "提交",      // 按钮文字 (字符串) - 按钮显示的文本，如"提交"、"保存"、"确认"
    type: "primary",   // 按钮类型 (字符串) - "primary": 主要按钮, "default": 默认按钮, "danger": 危险按钮
    position: "bottom", // 按钮位置 (字符串) - "bottom": 底部固定, "normal": 正常位置
    plain: false,      // 是否朴素按钮 (布尔值) - true: 朴素样式, false: 填充样式
  },
};
deepAssign(config, props.config, { postData: route.query });

if (config.postData && config.postData.id) {
  config.postData.id = parseInt(config.postData.id, 10);
}

if (props.form) {
  deepAssign(form, props.form);
}

if (!config.groupForm && config.form) {
  config.groupForm = [0, config.form.length - 1];
}
let url = null;
let loading = ref(false);
let deleteLoading = ref(false);
let emits = defineEmits(["onSubmit"]);

// 缓存相关变量和函数
const cacheKey = computed(() => route.path);
const isAddMode = computed(() => !("id" in config.postData));
const shouldEnableCache = computed(() => props.enableAutoCache && isAddMode.value);

// 缓存工具函数
const saveFormCache = () => {
  if (!shouldEnableCache.value) return;

  try {
    const formData = toRaw(form);
    const cacheData = JSON.stringify(formData);
    localStorage.setItem(cacheKey.value, cacheData);
    console.log('表单数据已缓存:', cacheKey.value);
  } catch (error) {
    console.warn('缓存表单数据失败:', error);
  }
};

const loadFormCache = () => {
  if (!shouldEnableCache.value) return false;

  try {
    const cachedData = localStorage.getItem(cacheKey.value);
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      // 使用 nextTick 确保在组件完全初始化后再恢复数据
      nextTick(() => {
        Object.assign(form, parsedData);
        console.log('已恢复缓存的表单数据:', cacheKey.value);
      });
      return true;
    }
  } catch (error) {
    console.warn('加载缓存数据失败:', error);
    // 如果缓存数据损坏，清除它
    clearFormCache();
  }
  return false;
};

const clearFormCache = () => {
  if (!shouldEnableCache.value) return;

  try {
    localStorage.removeItem(cacheKey.value);
    console.log('已清除表单缓存:', cacheKey.value);
  } catch (error) {
    console.warn('清除缓存失败:', error);
  }
};

// 计算属性：判断是否存在 id
const hasId = computed(() => {
  return config.postData && config.postData.id && config.postData.id !== '';
});

// 计算属性：按钮显示逻辑
const buttonConfig = computed(() => {
  const isAdd = props.pageType === 'add';
  const isDetail = props.pageType === 'detail';

  if (isAdd) {
    // add页面逻辑：区分新增和修改场景
    return {
      showSingleButton: true,
      showDoubleButtons: false,
      singleButtonText: hasId.value ? '保存' : '提交'
    };
  } else if (isDetail && hasId.value) {
    // detail页面逻辑（必须有id）：显示删除和修改两个按钮
    return {
      showSingleButton: false,
      showDoubleButtons: true,
      singleButtonText: ''
    };
  } else {
    // 其他情况不显示按钮
    return {
      showSingleButton: false,
      showDoubleButtons: false,
      singleButtonText: ''
    };
  }
});
// 初始化表单默认值（递归处理子组件）
const initDefaultValues = () => {
  if (config.form && Array.isArray(config.form)) {
    config.form.forEach(field => {
      // console.log(field,'fieldfield')
      // 处理顶层字段的默认值
      if (field.default !== undefined && form[field.key] === undefined) {
        form[field.key] = field.default;
      }

      // 递归处理子组件的默认值
      if (field.child && field.child.map) {
        // 遍历所有可能的子表单配置
        Object.keys(field.child.map).forEach(mapKey => {
          const childFields = field.child.map[mapKey];
          if (Array.isArray(childFields)) {
            childFields.forEach(childField => {
              if (childField.default !== undefined && form[childField.key] === undefined) {
                form[childField.key] = childField.default;
              }
            });
          }
        });
      }
      // 处理直接的子表单字段
      if (field.child && field.child.form && Array.isArray(field.child.form)) {
        field.child.form.forEach(childField => {
          if (childField.default !== undefined && form[childField.key] === undefined) {
            form[childField.key] = childField.default;
          }
        });
      }
    });
  }
};
var getInfo = () => {
  loading.value = true;
  proxy
    .$get(config.curl.info, config.postData)
    .then((res) => {
      if (res.code === 200) {
        res = res.data;
        handleJson(res, false, config.form);

        // 处理有keyMap配置的字段，将服务器数据转换为组件格式
        // if (config.form && Array.isArray(config.form)) {
        //   config.form.forEach(field => {
        //     if (field.opts && field.opts.keyMap && typeof field.opts.keyMap === 'object') {
        //       // 检查是否需要从其他字段组合数据
        //       if (res[field.key] === undefined) {
        //         // 尝试从keyMap映射的字段中构建数据
        //         const keyMapEntries = Object.entries(field.opts.keyMap);
        //         const hasAllMappedFields = keyMapEntries.every(([key]) => res[key] !== undefined);

        //         if (hasAllMappedFields) {
        //           // 构建组合数据对象
        //           const combinedData = {};
        //           keyMapEntries.forEach(([key]) => {
        //             combinedData[key] = res[key];
        //           });

        //           // 使用handleFormData处理数据转换
        //           const processedData = handleFormData(field, combinedData, false, res);
        //           res[field.key] = processedData;

        //           console.log(`字段 ${field.key} 数据转换:`, {
        //             old: combinedData,
        //             new: processedData
        //           });
        //         }
        //       } else {
        //         // 字段已存在，直接处理
        //          handleJson(res, false, config.form);

        //       }
        //     }
        //   });
        // }

        if (config.format) {
          config.format(res);
        }
        Object.assign(form, res);
      } else {
        throw res.msg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      loading.value = false;
      isShow.value = true;
    });
};
if ("id" in config.postData) {
  url = config.curl.edit;
  getInfo();
} else {
  url = config.curl.add;
  // 在新增模式下，尝试加载缓存数据
  loadFormCache();
  initDefaultValues();
  isShow.value = true;
}
// 防抖定时器
let saveTimeoutId = null;

// 监听表单数据变化，实时保存缓存
watch(form, () => {
  // 只在新增模式下且启用缓存时保存
  if (shouldEnableCache.value) {
    // 使用防抖，避免频繁保存
    clearTimeout(saveTimeoutId);
    saveTimeoutId = setTimeout(() => {
      saveFormCache();
    }, 500); // 500ms 防抖
  }
}, { deep: true });

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  clearTimeout(saveTimeoutId);
});

// 删除功能
const onDelete = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定将立即删除，此操作不可逆，是否继续？',
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24',
    });

    // 用户确认删除
    if (config.curl && config.curl.del) {
      deleteLoading.value = true;
      proxy
        .$postDel(config.curl.del, config.postData)
        .then((res) => {
          if (res.code === 200) {
            showToast("删除成功~");
            setTimeout(() => {
              app.dishFoodList = null;
              router.go(-1);
            }, 2000);
          } else {
            throw res.msg;
          }
        })
        .catch((err) => {
          console.log(err);
          showToast(err);
        })
        .finally(() => {
          deleteLoading.value = false;
        });
    } else {
      showToast("删除接口未配置");
    }
  } catch (error) {
    // 用户取消删除，不做任何操作
    console.log('用户取消删除');
  }
};

// 修改按钮点击处理（仅在detail页面使用）
const onEdit = () => {
  if (props.editRedirectConfig && props.editRedirectConfig.path) {
    // 构建跳转参数
    const query = {
      ...route.query,
    };

    router.push({
      path: props.editRedirectConfig.path,
      query: query
    });
  } else {
    showToast("修改页面路径未配置");
  }
};

// 递归收集所有表单字段的key，包括keyMap映射的字段
const collectFormFieldKeys = (formFields) => {
  const keys = new Set();

  if (!formFields || !Array.isArray(formFields)) {
    return keys;
  }

  formFields.forEach(field => {
    if (field.key) {
      keys.add(field.key);
    }

    // 处理keyMap映射的字段
    if (field.opts && field.opts.keyMap) {
      if (typeof field.opts.keyMap === 'string') {
        // keyMap是字符串类型，添加该字段
        keys.add(field.opts.keyMap);
      } else if (Array.isArray(field.opts.keyMap)) {
        // keyMap是数组类型，处理数组中的映射
        field.opts.keyMap.forEach(mapItem => {
          if (typeof mapItem === 'string') {
            keys.add(mapItem);
          } else if (typeof mapItem === 'object' && mapItem !== null) {
            // 如果数组项是对象，添加对象的所有值
            Object.values(mapItem).forEach(value => {
              if (typeof value === 'string') {
                keys.add(value);
              }
            });
          }
        });
      } else if (typeof field.opts.keyMap === 'object' && field.opts.keyMap !== null) {
        // keyMap是对象类型，添加对象的所有键和值
        Object.keys(field.opts.keyMap).forEach(key => keys.add(key));
        Object.values(field.opts.keyMap).forEach(value => {
          if (typeof value === 'string') {
            keys.add(value);
          }
        });
      }
    }

    // 处理子表单字段
    if (field.child) {
      // 处理 child.form 字段
      if (field.child.form && Array.isArray(field.child.form)) {
        const childKeys = collectFormFieldKeys(field.child.form);
        childKeys.forEach(key => keys.add(key));
      }

      // 处理 child.formChild 字段
      if (field.child.formChild && Array.isArray(field.child.formChild)) {
        const childKeys = collectFormFieldKeys(field.child.formChild);
        childKeys.forEach(key => keys.add(key));
      }

      // 处理 child.map 映射字段
      if (field.child.map && typeof field.child.map === 'object') {
        Object.keys(field.child.map).forEach(mapKey => {
          const mappedFields = field.child.map[mapKey];
          if (Array.isArray(mappedFields)) {
            const childKeys = collectFormFieldKeys(mappedFields);
            childKeys.forEach(key => keys.add(key));
          }
        });
      }

      // 处理 child.mapChild 映射字段
      if (field.child.mapChild && typeof field.child.mapChild === 'object') {
        Object.keys(field.child.mapChild).forEach(mapKey => {
          const mappedFields = field.child.mapChild[mapKey];
          if (Array.isArray(mappedFields)) {
            const childKeys = collectFormFieldKeys(mappedFields);
            childKeys.forEach(key => keys.add(key));
          }
        });
      }
    }
  });

  return keys;
};

// 判断值是否为空
const isEmpty = (value) => {
  if (value === null || value === undefined) {
    return true;
  }
  if (typeof value === 'string' && value.trim() === '') {
    return true;
  }
  if (Array.isArray(value) && value.length === 0) {
    return true;
  }
  if (typeof value === 'object' && Object.keys(value).length === 0) {
    return true;
  }
  return false;
};

// 过滤提交数据，只保留表单字段和id字段，并过滤空值
const filterSubmitData = (formData) => {
  // 收集所有表单字段的key，包括keyMap映射的字段
  const formFieldKeys = collectFormFieldKeys(config.form);

  // 添加id字段（如果存在）
  if (config.postData && config.postData.id) {
    formFieldKeys.add('id');
  }

  // 过滤数据，只保留表单字段和id字段，并排除空值
  const filteredData = {};
  Object.keys(formData).forEach(key => {
    if (formFieldKeys.has(key)) {
      const value = formData[key];
      // id字段始终保留，即使为空
      if (key === 'id' || !isEmpty(value)) {
        filteredData[key] = value;
      }
    }
  });

  console.log('表单字段keys（包含keyMap）:', Array.from(formFieldKeys));
  console.log('过滤前数据:', formData);
  console.log('过滤后数据（已排除空值）:', filteredData);

  return filteredData;
};

const onSubmit = () => {
  let postData = toRaw(form);
  handleJson(postData, true);

  // 过滤数据，只保留表单字段和id字段
  const filteredData = filterSubmitData(postData);

  // console.log("提交数据---原始数据---》", e, form, postData, url);
  emits("onSubmit", filteredData);
  if (url) {
    loading.value = true;
    proxy
      .$post(url, Object.assign(filteredData, config.postData))
      .then((res) => {
        if (res.code === 200) {
          // 保存成功后清除缓存
          clearFormCache();
          showToast("操作成功~");
          setTimeout(() => {
            app.dishFoodList = null
            router.go(-1);
          }, 2000);
        } else {
          throw res.msg;
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
</script>
<style lang="scss" scoped>
.wrapper-form {
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .desc-wrapper {
    margin: -15px 12px 4px 16px;
    border-radius: 12px;
    overflow: hidden;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .button-normal {
    margin: 16px;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .button-pos {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    z-index: 999;
    // border-top-left-radius: 12px;
    // border-top-right-radius: 12px;
  }

  .button-group {
    display: flex;
    gap: 12px;

    .delete-button {
      flex: 1;
      color: #666;
      border-color: #ddd;
      background: #fff;
    }

    .submit-button {
      flex: 1;
    }

    .edit-button {
      flex: 1;
    }
  }
}
</style>
