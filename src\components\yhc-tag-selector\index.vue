<template>
  <div class="wrapper">
    <van-cell-group>
      <van-field :name="config.key" :label="config.label" :required="config.required" :rules="config.rules"
        :disabled="config.disabled" :label-width="config.labelWidth" :border="config.border" readonly>
      </van-field>
      <!-- <template #input> -->
      <div class="tag-selector">
        <div class="tag-list">
          <div v-for="tag in presetTags" :key="`preset-${tag[config.opts.contrast_key]}`"
            :class="['tag-item', { 'tag-selected': isTagSelected(tag[config.opts.contrast_key]) }]"
            @click="togglePresetTag(tag[config.opts.contrast_key])">
            {{ tag[config.opts.text_key] }}
            <van-icon name="cross" class="tag-delete" @click.stop="deleteCustomTag(tag.id)"
              v-if="tag.is_default === 0" />
          </div>

          <!-- 自定义标签 -->
          <!-- <div v-if="customTags.length > 0">
            <div class="tag-list">
              <div v-for="tag in customTags" :key="`custom-${tag.id}`"
                :class="['tag-item', 'tag-custom', { 'tag-selected': isTagSelected(tag[config.opts.contrast_key]) }]"
                @click="toggleCustomTag(tag.id)">
                {{ tag.name }}
                <van-icon name="cross" class="tag-delete" @click.stop="deleteCustomTag(tag.id)" v-if="tag.is_default===1" />
              </div>
            </div>
          </div> -->
        </div>
        <div class="add-tag-section">
          <van-field v-model="newTagName" placeholder="添加自定义标签" class="add-tag-input" clearable
            @keyup.enter="addCustomTag" v-if="!config.disabled" />
          <van-button type="primary" size="small" @click="addCustomTag" :disabled="!newTagName.trim()"
            style="margin-left: -10px;" v-if="!config.disabled">
            添加
          </van-button>
        </div>
        <!-- 添加自定义标签输入框
        <div class="add-tag-section">
          <van-field v-model="newTagName" placeholder="输入自定义标签" :maxlength="config.opts.maxTagLength"
            @keyup.enter="addCustomTag" />
          <van-button type="primary" size="small" :disabled="!newTagName.trim()" @click="addCustomTag">
            添加
          </van-button>
        </div> -->
      </div>
      <!-- </template> -->
    </van-cell-group>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch, computed, onMounted } from "vue";
import { deepAssign } from "@/untils";
import { showToast, showConfirmDialog } from "vant";

const { proxy } = getCurrentInstance();

// 默认配置
let config = {
  // 基础配置
  label: "标签选择",       // 字段标签 (字符串) - 显示在标签选择器上方的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"tags", "labels"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  rules: [],               // 验证规则 (数组) - 表单验证规则配置
  defaultSelected: [],     // 默认选中的标签ID数组 (数组) - 初始选中的标签ID列表

  // 样式配置
  border: false,           // 是否显示边框 (布尔值) - true: 显示边框, false: 无边框
  labelWidth: '',          // 标签宽度 (字符串) - 标签区域的宽度，如"80px", "100px"

  // 选项配置
  opts: {
    url: "",               // 获取预设标签的API地址 (字符串) - 例: "/api/tags" - 获取标签列表的接口
    postData: {},          // 请求参数 (对象) - 发送给接口的参数，如: {category: 1, status: 'active'}
    text_key: "name",      // 显示字段 (字符串) - 标签显示文字对应的数据字段名
    contrast_key: "id",    // 值字段 (字符串) - 标签值对应的数据字段名
    defaultList: [],       // 默认预设标签数据 (数组) - 本地默认标签数据，当API无数据时使用
    maxTagLength: 10,      // 自定义标签最大长度 (数字) - 限制用户输入标签的最大字符数
    maxCustomTags: 20,     // 最大自定义标签数量 (数字) - 限制用户可创建的自定义标签数量
  },

  // 添加接口配置
  addApi: {
    url: "",               // 添加接口地址 (字符串) - 例: "/api/tags/add" - 创建新标签的API
    method: "POST",        // 请求方法 (字符串) - "POST": POST请求, "PUT": PUT请求
    postData: {},          // 额外请求参数 (对象) - 提交时的额外参数，如: {category_id: 1}
    refreshAfterAdd: true, // 添加成功后是否刷新列表 (布尔值) - true: 自动刷新, false: 不刷新
    successCallback: null, // 成功回调函数 (函数) - 添加成功后的自定义回调
    errorCallback: null,   // 错误回调函数 (函数) - 添加失败后的自定义回调
  },

  // 删除接口配置
  deleteApi: {
    url: "",               // 删除接口地址 (字符串) - 例: "/api/tags/delete" - 删除标签的API
    method: "POST",        // 请求方法 (字符串) - "POST": POST请求, "DELETE": DELETE请求
    postData: {},          // 额外请求参数 (对象) - 删除时的额外参数
  },
};

const props = defineProps({
  config: Object,
  form: Object,
});

// 合并配置
props.config && deepAssign(config, props.config);

// 定义事件
const emit = defineEmits(["change"]);

// 响应式数据
const data = reactive({
  loading: false,
  error: false,
});

// 预设标签列表
const presetTags = ref([]);
// 自定义标签列表
const customTags = ref([]);
// 新标签名称
const newTagName = ref("");

// 初始化表单值
if (!props.form[config.key]) {
  props.form[config.key] = [];
}

// 计算选中的标签ID数组
const selectedTagIds = computed({
  get () {
    return props.form[config.key] || [];
  },
  set (value) {
    props.form[config.key] = value;
    // 触发change事件
    emit("change", {
      component: "yhc-tag-selector",
      key: config.key,
      value: value
    });
  }
});

// 判断标签是否被选中
const isTagSelected = (tagId) => {

  return selectedTagIds.value.includes(tagId);
};

// 切换预设标签选中状态
const togglePresetTag = (tagId) => {
  if (config.disabled) return;
  const currentSelected = [...selectedTagIds.value];
  console.log("currentSelected", currentSelected);
  const index = currentSelected.indexOf(tagId);

  if (index > -1) {
    // 取消选中
    currentSelected.splice(index, 1);
  } else {
    // 选中
    currentSelected.push(tagId);
  }
  // 去掉 数字字符串
  selectedTagIds.value = currentSelected;
};

// 切换自定义标签选中状态
const toggleCustomTag = (tagId) => {
  if (config.disabled) return;
  togglePresetTag(tagId); // 使用相同的逻辑
};





// 加载预设标签数据
const loadPresetTags = () => {
  if (config.opts.url) {
    data.loading = true;
    let query = { ...config.opts.postData };

    proxy
      .$get(config.opts.url, query)
      .then((res) => {
        if (res.code === 200) {
          presetTags.value = res.data || [];
        } else {
          data.error = true;
          throw res.msg || "获取标签数据失败";
        }
      })
      .catch((err) => {
        data.error = true;
        console.log(err);
        showToast(err);
      })
      .finally(() => {
        data.loading = false;
      });
  } else {
    // 使用默认数据
    presetTags.value = JSON.parse(JSON.stringify(config.opts.defaultList));
  }
};
// 删除自定义标签
const deleteCustomTag = async (tagId) => {
  if (config.disabled) return;
  if (!config.deleteApi.url) {
    showToast('未配置删除接口');
    return;
  }
  try {
    await showConfirmDialog({
      title: '删除标签',
      message: '所有包间的该标签信息将都会删除',
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24',
    });

    // 用户确认删除
    proxy
      .$postDel(config.deleteApi.url, {
        id: tagId
      })
      .then((res) => {
        if (res.code === 200) {
          showToast("删除成功~");
          loadPresetTags()
        } else {
          throw res.msg;
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
        deleteLoading.value = false;
      });
  } catch (error) {
    // 用户取消删除，不做任何操作
    console.log('用户取消删除');
  }

  // // 从自定义标签列表中移除
  // const index = customTags.value.findIndex(tag => tag.id === tagId);
  // if (index > -1) {
  //   customTags.value.splice(index, 1);
  // }

  // // 从选中列表中移除
  // const currentSelected = [...selectedTagIds.value];
  // const selectedIndex = currentSelected.indexOf(tagId);
  // if (selectedIndex > -1) {
  //   currentSelected.splice(selectedIndex, 1);
  //   selectedTagIds.value = currentSelected;
  // }

  // showToast("标签删除成功");
};
// 添加自定义标签
const addCustomTag = () => {
  const title = newTagName.value.trim();
  const tag = {
    title: title,
  };
  if (!title) {
    showToast("请输入标签名称");
    return;
  }
  if (!config.addApi.url) {
    showToast('未配置添加接口');
    return;
  }
  // 准备提交数据
  const postData = { ...tag, ...config.addApi.postData };
  // 调用添加接口
  proxy
    .$post(config.addApi.url, postData)
    .then((res) => {
      if (res.code === 200) {
        newTagName.value = "";
        loadPresetTags()
        showToast('添加成功');
      } else {
        throw res.msg
      }
    })
  // if (tagName.length > config.opts.maxTagLength) {
  //   showToast(`标签名称不能超过${config.opts.maxTagLength}个字符`);
  //   return;
  // }

  // // 检查是否已存在相同名称的标签
  // const existsInPreset = presetTags.value.some(tag => tag[config.opts.text_key] === tagName);
  // const existsInCustom = customTags.value.some(tag => tag.name === tagName);

  // if (existsInPreset || existsInCustom) {
  //   showToast("标签名称已存在");
  //   return;
  // }

  // if (customTags.value.length >= config.opts.maxCustomTags) {
  //   showToast(`最多只能添加${config.opts.maxCustomTags}个自定义标签`);
  //   return;
  // }

  // // 生成临时ID（负数，避免与后端ID冲突）
  // const tempId = -(Date.now() + Math.random());

  // // 添加到自定义标签列表
  // customTags.value.push({
  //   id: tempId,
  //   name: tagName,
  //   isCustom: true
  // });

  // // 自动选中新添加的标签
  // const currentSelected = [...selectedTagIds.value];
  // currentSelected.push(tempId);
  // selectedTagIds.value = currentSelected;

  // // 清空输入框
  // newTagName.value = "";

  // showToast("标签添加成功");
};
// 设置默认选中标签
const setDefaultSelected = () => {
  if (config.defaultSelected && config.defaultSelected.length > 0) {
    selectedTagIds.value = [...config.defaultSelected];
  }
};

// 组件挂载时初始化
onMounted(() => {
  loadPresetTags();
  setDefaultSelected();
});

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    deepAssign(config, newConfig);
    loadPresetTags();
  }
}, { deep: true });
</script>

<style lang="scss" scoped>
.wrapper {

  // 标签选择器样式
  .tag-selector {
    width: 100%;
    padding-left: 10px;

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .tag-item {
        position: relative;
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 14px;
        color: #646566;
        background-color: #f7f8fa;
        border: 1px solid #ebedf0;
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;

        &:hover {
          background-color: #f2f3f5;
        }

        &.tag-selected {
          background-color: #1989fa;
          color: white;
          border-color: #1989fa;
        }

        &.tag-custom {
          padding-right: 28px; // 为删除按钮留出空间

          .tag-delete {
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.8);

            &:hover {
              color: white;
            }
          }

          &:not(.tag-selected) .tag-delete {
            color: #969799;

            &:hover {
              color: #646566;
            }
          }
        }
      }
    }

    .add-tag-section {
      display: flex;
      align-items: center;
      gap: 15px;
      padding-bottom: 15px;
      width: 95%;

      .add-tag-input {
        border: 1px solid #f2f3f5;
        flex: 1;

        :deep(.van-field__control) {
          font-size: 14px;
        }

        :deep(.van-cell) {
          padding: 8px 12px;
          background-color: #f7f8fa;
          border-radius: 8px;
        }

        :deep(.van-field__body) {
          border: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .wrapper {
    .tag-selector-content {
      .tag-list {
        gap: 6px;

        .tag-item {
          padding: 4px 8px;
          font-size: 12px;

          &.tag-custom {
            padding-right: 24px;

            .tag-delete {
              right: 4px;
              font-size: 10px;
            }
          }
        }
      }

      .tag-input-section {
        .tag-input-wrapper {
          gap: 6px;

          .add-button {
            height: 28px;
            padding: 0 8px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
