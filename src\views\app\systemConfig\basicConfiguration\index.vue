<template>
  <div class="basic-config-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="基础配置" left-arrow @click-left="$router.go(-1)" /> -->

    <!-- 支付配置 -->
    <van-cell-group title="支付配置" inset>
      <van-cell
        title="固定付款码"
        :label="config.fixedPaymentCode ? '开启' : '关闭：开启后用户付款码内容固定不变'"
      >
        <template #right-icon>
          <van-switch v-model="config.fixedPaymentCode" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell
        title="补贴账户转账"
        :label="config.subsidyTransfer ? '开启' : '关闭：开启后用户可对补贴账户余额转给他人'"
      >
        <template #right-icon>
          <van-switch v-model="config.subsidyTransfer" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell
        title="匿名评价"
        :label="config.anonymousReview ? '开启' : '关闭：开启后用户评价不可显示姓名'"
      >
        <template #right-icon>
          <van-switch v-model="config.anonymousReview" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell
        title="储值账户充值"
        :label="config.storedValueRecharge ? '开启' : '关闭'"
      >
        <template #right-icon>
          <van-switch v-model="config.storedValueRecharge" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell
        title="储值账户提现"
        :label="config.storedValueWithdraw ? '开启' : '关闭'"
      >
        <template #right-icon>
          <van-switch v-model="config.storedValueWithdraw" @change="saveConfig" />
        </template>
      </van-cell>

      <van-cell
        title="先享后付"
        :label="config.enjoyFirstPayLater ? '开启：用户可使用先享后付提前消费' : '关闭'"
      >
        <template #right-icon>
          <van-switch v-model="config.enjoyFirstPayLater" @change="saveConfig" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 限额配置 -->
    <van-cell-group title="限额配置" inset>
      <van-cell
        title="补贴账户上限"
        :label="config.subsidyLimitEnabled ? `有上限：${config.subsidyLimitAmount}元，用户补贴账户最多${config.subsidyLimitAmount}元` : '关闭：无限制'"
      >
        <template #right-icon>
          <van-switch v-model="config.subsidyLimitEnabled" @change="saveConfig" />
        </template>
      </van-cell>

      <!-- 补贴账户上限金额设置 -->
      <van-cell
        v-if="config.subsidyLimitEnabled"
        title="上限金额(元)"
        is-link
        :value="config.subsidyLimitAmount + '元'"
        @click="showSubsidyLimitPicker = true"
      />

      <van-cell
        title="充值最少金额"
        :label="config.minRechargeEnabled ? `有限制：${config.minRechargeAmount}元，用户单次充值最少${config.minRechargeAmount}元` : '关闭：无限制'"
      >
        <template #right-icon>
          <van-switch v-model="config.minRechargeEnabled" @change="saveConfig" />
        </template>
      </van-cell>

      <!-- 充值最少金额设置 -->
      <van-cell
        v-if="config.minRechargeEnabled"
        title="最少金额(元)"
        is-link
        :value="config.minRechargeAmount + '元'"
        @click="showMinRechargePicker = true"
      />
    </van-cell-group>

    <!-- 补贴账户上限金额选择器 -->
    <van-popup v-model:show="showSubsidyLimitPicker" position="bottom">
      <van-picker
        :columns="amountColumns"
        @confirm="onSubsidyLimitConfirm"
        @cancel="showSubsidyLimitPicker = false"
      />
    </van-popup>

    <!-- 充值最少金额选择器 -->
    <van-popup v-model:show="showMinRechargePicker" position="bottom">
      <van-picker
        :columns="amountColumns"
        @confirm="onMinRechargeConfirm"
        @cancel="showMinRechargePicker = false"
      />
    </van-popup>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { showToast } from 'vant'

// 配置数据
const config = reactive({
  fixedPaymentCode: true,
  subsidyTransfer: false,
  anonymousReview: true,
  storedValueRecharge: true,
  storedValueWithdraw: false,
  enjoyFirstPayLater: false, // 先享后付
  subsidyLimitEnabled: true,
  subsidyLimitAmount: 100,
  minRechargeEnabled: true,
  minRechargeAmount: 50
})

// 弹窗控制
const showSubsidyLimitPicker = ref(false)
const showMinRechargePicker = ref(false)

// 金额选择器选项
const amountColumns = [
  { text: '10元', value: 10 },
  { text: '20元', value: 20 },
  { text: '50元', value: 50 },
  { text: '100元', value: 100 },
  { text: '200元', value: 200 },
  { text: '500元', value: 500 },
  { text: '1000元', value: 1000 }
]

// 保存配置
const saveConfig = () => {
  console.log('保存配置:', config)
  showToast('配置已保存')
  // 这里应该调用接口保存配置
}



// 补贴账户上限确认
const onSubsidyLimitConfirm = ({ selectedValues }) => {
  config.subsidyLimitAmount = selectedValues[0]
  showSubsidyLimitPicker.value = false
  saveConfig()
}

// 充值最少金额确认
const onMinRechargeConfirm = ({ selectedValues }) => {
  config.minRechargeAmount = selectedValues[0]
  showMinRechargePicker.value = false
  saveConfig()
}





// 页面加载时获取当前配置
onMounted(() => {
  loadCurrentConfig()
})

// 加载当前配置
const loadCurrentConfig = async () => {
  try {
    console.log('正在加载当前配置...')

    // 实际项目中，这里应该调用接口获取配置
    // const response = await proxy.$get('/api/system/config/info')
    // if (response.code === 200) {
    //   Object.assign(config, response.data)
    // }

    console.log('配置加载完成')
  } catch (error) {
    console.error('加载配置失败:', error)
    showToast('加载配置失败')
  }
}
</script>

<style lang="scss" scoped>
.basic-config-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 20px;

  .placeholder-text {
    color: #969799;
    font-size: 14px;
  }

  .dialog-content {
    padding: 20px;

    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      text-align: center;
    }

    .dialog-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;

      .van-button {
        flex: 1;
      }
    }
  }
}

// 自定义 Vant 组件样式
:deep(.van-cell-group) {
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .van-cell-group__title {
    padding: 12px 16px;
    background: #f8f9fa;
    font-weight: 600;
    color: #646566;
    border-bottom: 1px solid #ebedf0;
  }
}

:deep(.van-switch--on) {
  background-color: #1989fa;
}

:deep(.van-image) {
  border-radius: 8px;
  border: 1px solid #ebedf0;
}

:deep(.van-popup) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.van-picker) {
  .van-picker__toolbar {
    background: #f8f9fa;
  }
}
</style>