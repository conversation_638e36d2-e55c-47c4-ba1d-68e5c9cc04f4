<template>
  <div>
    <div class="content">
      <!-- <div class="window_title">{{ postData.window_title }}</div> -->
      <van-cell-group inset class="cell-group">
        <van-cell center title="同步所有窗口">
          <template #right-icon>
            <van-switch
              v-model="window_all"
              active-value="1"
              inactive-value="0"
            />
          </template>
        </van-cell>
      </van-cell-group>
      <div class="dish-header">
        <div class="selected-dishes">
            <div class="selected-text">
                已选菜品（{{list.length ? list.length : 0}}）
            </div>
        </div>
            <van-button type="primary" icon="plus" size="mini" @click="selectDishes">
              {{ list.length == 0 ? '选择菜品' : '继续选择' }}
            </van-button>
    </div>
      <van-cell-group inset class="cell-group">
        <template v-if="list.length != 0">
          <draggable
            v-model="list"
            handle=".move"
            @start="onStart"
            @end="onEnd"
            animation="300"
          >
            <template #item="{ element }">
              <div :key="element.id" class="card">
                <div class="left">
                  <!-- -->
                  <div class="move" v-if="sort_type" >
                       <img src="/public/img/move.png" alt=""  style="width: 15px; margin-top: 20px; margin-right: 10px;"/>
                     </div>   
                  <template v-if="element.image"
                    ><img :src="element.image" alt="" class="img"
                  /></template>
                  <template v-else>
                    <div class="img">{{ element.title[0] }}</div>
                  </template>
                  <div class="desc">
                    <div class="title">{{ element.title }}</div>
                    <div class="info">
                      库存：{{ element.stock ? element.stock : "无限" }} &nbsp;&nbsp;&nbsp;&nbsp;限购：{{ element.quota ? element.quota : "无限" }}
                    </div>
                    <!-- <div class="info">
                      限购：{{ element.quota ? element.quota : "无限" }}
                    </div> -->
                  </div>
                </div>
                <div class="right" :style="`margin-right:${sort_type ? 0 : 16}px`">
                  <div>￥{{ (element.price*1).toFixed(1) }}</div>

                </div>
              </div>
            </template>
          </draggable>
        </template>
      </van-cell-group>
    </div>
    <template v-if="!dishes.isPublic">
      <van-action-bar class="bar">
        <van-button class="submit" type="primary" @click="publish">
          发布菜品
        </van-button>
      </van-action-bar>
    </template>
    <template v-else>
      <van-action-bar class="bar">
        <van-button class="cancel button" @click="unpublish">
          取消发布
        </van-button>
        <van-button class="publish button" type="primary" @click="publish">
          发布菜品
        </van-button>
      </van-action-bar>
    </template>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, watch, onMounted } from "vue";
import { showToast, showFailToast, showSuccessToast } from "vant";
const { proxy } = getCurrentInstance();
import { useRouter, useRoute } from "vue-router";
import { dishesInfo } from "@/store/dishes_public";
import draggable from "vuedraggable";
// 路由跳转
const route = useRoute();
const router = useRouter();

let postData = ref({
  window_title: "",
});

// 是否同步所有
let window_all = ref(0);
let sort_type = ref(0);

// 菜品列表
let list = ref([]);

// 是否有菜品
// let isHave = ref(false);

// pinia 上的菜品信息
let dishes = dishesInfo();

// 是否传过来的数据
// let isSelect = ref(false);

onMounted(() => {
  // console.log("获取信息 --->", route.query);
  if (dishes.select) {
    // console.log("查看传递过来的信息 --->", dishes.dishesInfo);
    dishes.dishesInfo.forEach((el) => {
      console.log("查看单个信息 -->", el);
      if (el.image) {
        let isSwitch = el.image.includes("[");
        if (isSwitch) {
          let image = JSON.parse(el.image);
          el.image = image[0];
        }
      }
    });
    list.value.push(...dishes.dishesInfo);
    dishes.select = false;
  } else {
    postData.value = route.query;
    dishes.releaseInfo = postData.value;
    getInfo();
  }
});

//拖拽结束的事件
const onEnd = (e) => {
  console.log("结束拖拽",e);
};
const onStartEnd = (e) => {
  sort_type.value = 1;
  showToast("已切换到手动排序")
};
const onSort = (e) => {
  sort_type.value = 0;
  showToast("已切换到分类排序")
};
const getInfo = () => {
  proxy
    .$get("dishes_release/get_info", postData.value)
    .then((res) => {
      // console.log("窗口详情 --->", res, dishes);
      if (res.code == 200) {
        // console.log("窗口详情 --->", res.result);
        list.value.push(...JSON.parse(res.data.dishess));
        dishes.dishesInfo = list.value;
        if (res.data.length > 0) {
          // isHave.value = true;
          dishes.isPublic = true;
        } else {
          dishes.isPublic = false;
        }
      } else {
        showFailToast(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 选择菜品
const selectDishes = () => {
  router.push({
    name: "dishes_list",
    query: {
      id: 1,
    },
  });
};

// 取消发布
const unpublish = () => {
  // console.log("取消发布 -->", dishes.releaseInfo);
  proxy
    .$post("dishes_release/post_del", dishes.releaseInfo)
    .then((res) => {
      if (res.errcode == 0) {
        // 取消成功
        showSuccessToast("取消成功");
        dishes.isPublic = false;
        setTimeout(() => {
          // router.push({
          //   path: "/app/dishes_public",
          // });
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 发布菜品
const publish = () => {
    console.log(dishes.releaseInfo,'dishes.releaseInfo')
  // 处理数据
  let postData = {
    date: dishes.releaseInfo.date,
    mealtime_id: dishes.releaseInfo.mealtime_id,
    stall_id: dishes.releaseInfo.stall_id,
    sync_all_stalls: window_all.value,
    dininghall_id:dishes.releaseInfo.dininghall_id,
    // sort_type: sort_type.value,
  };
  let dishess = [];
  // console.log("选择菜品 --->", dishes.dishesInfo);
  list.value.forEach((el) => {
    let item = {
      id: el.id,
      title: el.title,
      category_id: el.category_id,
      category_title: el.category_title,
      price: el.price,
      image: el.image,
      // desc: el.desc,
      // stock_limit: el.stock ? "1" : "0",
      // stock: el.stock,
      // quota: el.quota,
    };
    dishess.push(item);
  });
  postData.dishess = JSON.stringify(dishess);
  proxy
    .$post("dishes_release/post_add", postData)
    .then((res) => {
      if (res.code == 200) {
        // 发布成功
        showSuccessToast("发布成功");
        setTimeout(() => {
          // router.push({
          //   path: "/app/dishes_public",
          // });
          router.go(-1);
        }, 1000);
      } else {
        showFailToast(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>

<style scoped lang="scss">
// 菜品头部样式
.dish-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 13px;
    // background: #fff;
    // border-bottom: 1px solid #ebedf0;

    .selected-dishes {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .selected-text {
            color: #323233;
            font-size: 14px;
            font-weight: 500;
            // line-height: 20px;
            // padding-bottom: 10px;
        }

        .selected-underline {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            // background: #1989fa;
            border-radius: 1px;
        }
    }

    // .add-dish-btn {
    //     :deep(.van-button) {
    //         // height: 28px;
    //         padding: 0 12px;
    //         font-size: 12px;
    //         border-radius: 4px;
    //     }
    // }
}
.content {
  height: 100vh;
  background-color: #f2f3f4;
  overflow: scroll;
  padding-bottom: 100px;

  .window_title {
    margin: 8px 0;
    margin-left: 5%;
    font-size: 14px;
    color: #9a9c9e;
  }

  .cell-group {
    margin-top: 8px;
  }
.type {
    width: 100px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.16);
    display: flex;
    justify-content: space-between;
    padding: 1px;
    border-radius: 4px;
    div {
      width: 50px;
      text-align: center;
      border-radius: 4px;
    }
  }
  .active {
    background: #fff;
    color: #000;
  }
  .card {
    border-bottom: 0.5px solid #eeeff1;
    display: flex;
    justify-content: space-between;
    padding: 16px;
    padding-right:0 ;

    .left {
      display: flex;
            .move {
            img {
           width: 6px;
          height: 12px;
        }
      }
      .img {
        margin-right: 8px;
        width: 60px;
        height: 60px;
        border-bottom: 0.5px solid #3c3d3f;
        border-radius: 5px;
        color: #fff;
        background-color: #1678ff;
        font-size: 20px;
        line-height: 60px;
        text-align: center;
      }

      .desc {
        .title {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 150px;
        }
        .info {
          margin-top: 8px;
          font-size: 14px;
          color: #a2a3a5;
        }
      }
    }

    .right {
      color: #ff5219;
      display: flex;
      align-items: center;
    }
  }
}
.bar {
  height: 60px;

  .submit {
    margin-top: 12px;
    margin-bottom: 5px;
    margin-left: 5%;
    width: 90%;
  }

  .button {
    width: 43%;
  }

  .cancel {
    margin-left: 5%;
    border-radius: 22px;
  }

  .publish {
    border-radius: 22px;
    margin-left: 4%;
  }
}
</style>
