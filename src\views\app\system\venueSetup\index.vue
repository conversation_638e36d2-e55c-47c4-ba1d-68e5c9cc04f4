<template>
    <div class="skeleton-demo-container">
        <!-- 列表组件演示 -->
        <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
            <!-- 新增按钮插槽 -->
            <template #header>
                <div class="add-button-container">
                    <div class="add-button" @click="onAddClick">
                        <img src="/img/add.svg" alt="新增" class="add-icon" />
                        <span>新增场所管理</span>
                    </div>
                </div>
            </template>
            <template #default="{ item, index }">
                <div class="demo-item" @click.stop="onCardClick(item)">
                    <div class="item-content">
                        <div class="item-title">{{ item.title }}</div>
                        <div class="item-desc">地址：{{ item.description }}</div>
                    </div>
                </div>
            </template>
        </yhc-list>
    </div>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'
const router = useRouter();

// 骨架屏配置
const skeletonConfig = reactive({
    isShow: false,
    count: 3,
    row: 2,
    rowWidth: ['100%', '60%', '80%'],
    avatar: true,
    avatarSize: '40px',
    avatarShape: 'round',
    title: true,
    titleWidth: '50%',
    duration: 500
})
// 列表组件引用
const listRef = ref(null)
// 拖拽排序数据
const sortableList = ref([])
// 列表配置
const listConfig = reactive({
    curl: {
        ls: '' // 留空，使用模拟数据
    },
    title: '场所管理',
    // details: '/dishSortDetail',
    // sort: true,
    postData: {},
    search: {
        isShow: true,
        isShowPopup: false
    },
    tabs: {
        isShow: false
    },
    button: {
        isShow: false,
    },
    skeleton: skeletonConfig,
    // 模拟数据格式化
    format: (data) => {
        // 这里可以对数据进行格式化处理
        console.log('格式化数据:', data)
        // 同步数据到拖拽列表
        sortableList.value = data
    },
    // 添加模拟数据标识
    mockData: true
})

// 新增按钮点击事件
const onAddClick = () => {
    router.push('/venueSetupAdd')
}
const onCardClick = (item) => {
    console.log('点击了：', item)
    router.push({ path: "/venueSetupDetail", query: { id: item.id } });
};
</script>
<style lang="scss" scoped>
.move {
    // 竖行中间位置
    transform: translateY(-27%);
    cursor: move;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        transition: opacity 0.2s ease;
        opacity: 0.6;

        &:hover {
            opacity: 1;
        }
    }
}

// 拖拽时的样式
.sortable-ghost {
    opacity: 0.5;
    background: #f0f0f0;
}

.sortable-chosen {
    background: #e8f4fd;
    border: 1px dashed #1989fa;
}

.sortable-drag {
    opacity: 0.8;
    transform: rotate(2deg);
}

.skeleton-demo-container {
    min-height: 100vh;
    background: #f7f8fa;
}

.add-button-container {
    padding: 16px;
    padding-bottom: 0;

    .add-button {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #fff;
        border-radius: 8px;

        .add-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        span {
            font-size: 16px;
            font-weight: normal;
            line-height: 22px;
            color: #323233;
        }
    }
}

.demo-item {
    display: flex;
    align-items: center;
    padding: 16px;
    margin: 16px;
    background: #fff;
    border-radius: 8px;

    .item-content {
        flex: 1;
        min-width: 0;

        /* 确保flex子项能够收缩 */
        .item-title {
            font-size: 16px;
            font-weight: 500;
            line-height: 22px;
            letter-spacing: normal;
            color: #171A1D;
            margin-bottom: 8px;
        }

        .item-desc {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
            font-weight: normal;
            line-height: 20px;
            letter-spacing: normal;
            color: #9E9E9E;
        }

        .item-time {
            font-size: 14px;
            font-weight: normal;
            line-height: 20px;
            letter-spacing: normal;
            color: #9E9E9E;
        }
    }

    .item-action {
        margin-left: 12px;
    }
}
</style>