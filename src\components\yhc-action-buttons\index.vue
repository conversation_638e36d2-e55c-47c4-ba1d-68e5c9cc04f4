<template>
  <div class="yhc-action-buttons">
    <div class="frame">
      <div class="frame-1">
        <div class="text" @click="handleClick('sync_day_to_week')">
          <div><span>同步日菜单<div class="_p"></div>至本周</span></div>
        </div>
      </div>
      <div class="frame-2">
        <div class="text-1" @click="handleClick('sync_week_to_month')">
          <div><span>同步周菜单<div class="_p"></div>至本月</span></div>
        </div>
      </div>
      <div class="frame-3">
        <div class="text-2" @click="handleClick('delete_sunday')">
          <div><span>删除<div class="_p"></div>周日</span></div>
        </div>
      </div>
      <div class="frame-4">
        <div class="text-3" @click="handleClick('delete_weekend')">
          <div><span>删除<div class="_p"></div>周六日</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义属性 - 菜单管理操作按钮组配置
const props = defineProps({
  // 基础配置
  disabled: {              // 是否禁用所有按钮 (布尔值) - true: 禁用所有操作按钮, false: 可正常操作
    type: Boolean,
    default: false
  },
  loading: {               // 加载状态 (字符串/布尔值) - 具体的操作类型字符串或false，用于显示对应按钮的加载状态
    type: [String, Boolean],
    default: false
  }
})

// 操作类型说明:
// - 'sync_day_to_week': 同步日菜单至本周
// - 'sync_week_to_month': 同步周菜单至本月
// - 'delete_sunday': 删除周日菜单
// - 'delete_weekend': 删除周六日菜单

// 定义事件
const emit = defineEmits(['click'])

// 处理按钮点击
const handleClick = (actionType) => {
  if (props.disabled || props.loading) return
  emit('click', actionType)
}
</script>

<style scoped lang="scss">
.yhc-action-buttons {
  padding: 2px;

  .frame {
    width: 100%;
    height: 62px;
    border-radius: 8px;
    display: flex;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    // 通用按钮样式
    .frame-1,
    .frame-2,
    .frame-3,
    .frame-4 {
      flex: 1; // 平均分配宽度
      height: 62px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      background: #FFFFFF;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:active {
        background-color: #e9ecef;
      }
    }

    .frame-1 {
      border-radius: 8px 0px 0px 8px;
      border-right: 1px solid #F2F3F5;

      .text {
        color: #2563EB;
        font-family: "PingFang SC";
        font-size: 13px;
        line-height: 16px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
    }

    .frame-2 {
      border-right: 1px solid #F2F3F5;

      .text-1 {
        color: #2563EB;
        font-family: "PingFang SC";
        font-size: 13px;
        line-height: 16px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
    }

    .frame-3 {
      border-right: 1px solid #F2F3F5;

      .text-2 {
        color: #DC2626;
        font-family: "PingFang SC";
        font-size: 13px;
        line-height: 16px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
    }

    .frame-4 {
      border-radius: 0px 8px 8px 0px;

      .text-3 {
        color: #DC2626;
        font-family: "PingFang SC";
        font-size: 13px;
        line-height: 16px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
    }
  }
}
</style>
