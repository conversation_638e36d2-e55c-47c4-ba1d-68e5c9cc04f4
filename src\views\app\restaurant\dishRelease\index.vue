<template>
  <div>
    <div class="content">
      <!-- 操作按钮组 -->
      <yhc-action-buttons :loading="currentLoadingOperation" @click="onActionButtonClick" />
      <calendar v-model:selectedDate="selectedDate" v-model:dateList="dateList"></calendar>

      <!-- 餐次和窗口列表 -->
      <div class="window-list">
        <template v-for="(el, i) in windowList.mealtime_stall_status" :key="i">
          <!-- 餐次标题 -->
          <div class="repast-title">
            {{ el.mealtime_title }}
          </div>
          <!-- 窗口列表 -->
          <template v-for="(item, index) in el.stalls" :key="index">
            <div class="window-item" @click="goWindow(item, el.mealtime_id)">
              <div class="window-content">
                <div class="window-title">
                  {{ item.stall_title }}
                </div>
                <div class="window-status" :class="getStatusClass(item.text)">
                  {{ item.text }}
                </div>
              </div>
              <div class="arrow-icon">
                <img
                  src="https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2025-07-15/VtTpzC4qEGfVVPGcyqAdg98DU7pqhBpE.png"
                  alt=""
                />
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>
    <!-- 预览弹窗组件 -->
    <batch-preview-dialog v-model:show="showPreviewDialog" :operation-type="currentOperationType"
      :selected-date="selectedDate" @confirm="onPreviewConfirm" @cancel="onPreviewCancel" />
  </div>
</template>

<script setup>
import BatchPreviewDialog from "./components/BatchPreviewDialog.vue";
import calendar from "./crud/calendar.vue";
import YhcActionButtons from "@/components/yhc-action-buttons/index.vue";
import { ref, getCurrentInstance, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { dishesInfo } from "@/store/dishes_public";
import {
  showFailToast,
  showLoadingToast,
  closeToast,
  showSuccessToast,
} from "vant";
import { useLoginStore } from "@/store/dingLogin";
import dayjs from "dayjs";

let dishes = dishesInfo();

// 日期格式化函数 - 月份和日期小于10时补零
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 年月格式化函数 - 月份小于10时补零
const formatYearMonth = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
};

const { proxy } = getCurrentInstance();
const router = useRouter();

let windowList = ref([]);
let dateList = ref(["2025-07-22","2025-07-21"]); // 临时测试数据

let selectedDate = ref(null);
if (dishes.selectedDate) {
  selectedDate.value = dayjs(dishes.selectedDate).toDate();
} else {
  selectedDate.value = new Date();
}

// 弹窗相关状态
const showPreviewDialog = ref(false);
const currentOperationType = ref('');
const currentLoadingOperation = ref(false);

onMounted(() => {
  getWindowList();
  getPublishlist();
});

watch(selectedDate, (val) => {
  if (val) {
    dishes.selectedDate = formatDate(val);
    getWindowList();
    getPublishlist();
  }
});

// 获取窗口信息
const getWindowList = () => {
  showLoadingToast({
    duration: 0,
    message: "加载中...",
    forbidClick: true,
  });
  proxy
    .$get("dishes_release/get_daily_detail", {
      date: formatDate(selectedDate.value),
        dininghall_id: localStorage.getItem('dininghall')
    })
    .then((res) => {
      // console.log("窗口信息结果 --->", res);
      closeToast();
      if (res.code == 200) {
        windowList.value = res.data;
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      closeToast();
      console.log(err);
    });
};

// 获取每月餐品是否发布信息
const getPublishlist = () => {
  proxy
    .$get("dishes_release/get_monthly_detail", {
      year_month: formatYearMonth(selectedDate.value),
      dininghall_id: localStorage.getItem('dininghall')
    })
    .then((res) => {
      console.log("每日菜品发布结果", res);
      if (res.code == 200) {
        dateList.value = res.data.published_dates;
        console.log("设置 dateList.value:", dateList.value);
      } else {
        showFailToast(res.errmsg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 处理操作按钮点击
const onActionButtonClick = (actionType) => {
  currentOperationType.value = actionType;
  showPreviewDialog.value = true;
};

// 预览弹窗确认
const onPreviewConfirm = (operationType) => {
  executeBatchOperation(operationType);
};

// 预览弹窗取消
const onPreviewCancel = () => {
  showPreviewDialog.value = false;
  currentOperationType.value = '';
};

// 执行批量操作
const executeBatchOperation = (operationType) => {
  const apiMap = {
    'sync_day_to_week': 'dishes_release/post_sync_to_week',
    'sync_week_to_month': 'dishes_release/post_sync_week_to_month',
    'delete_sunday': 'dishes_release/post_delete_weekend',
    'delete_weekend': 'dishes_release/post_delete_weekend'
  };
  const data ={
    'sync_day_to_week': {
      dininghall_id: parseInt(localStorage.getItem('dininghall')),
      source_date: formatDate(selectedDate.value),
    },
    'sync_week_to_month': {
      dininghall_id: parseInt(localStorage.getItem('dininghall')),
      // 获取本周开始时间，结束时间
      start_date: formatDate(new Date(selectedDate.value.getFullYear(), selectedDate.value.getMonth(), selectedDate.value.getDate() - selectedDate.value.getDay())),
      end_date: formatDate(new Date(selectedDate.value.getFullYear(), selectedDate.value.getMonth(), selectedDate.value.getDate() - selectedDate.value.getDay() + 6)),
    },
    'delete_sunday': {
      dininghall_id: parseInt(localStorage.getItem('dininghall')),
      delete_type: 1,
      // 获取本月 年月格式
      year_month:formatYearMonth(selectedDate.value),
    },
    'delete_weekend': {
      dininghall_id: parseInt(localStorage.getItem('dininghall')),
      delete_type: 2,
      // 获取本月 年月格式
      year_month:formatYearMonth(selectedDate.value),
    }
  }
  const api = apiMap[operationType];
  const params = data[operationType];
  // const data 
  if (!api) return;

  // 设置按钮加载状态
  currentLoadingOperation.value = operationType;

  showLoadingToast({
    duration: 0,
    message: "执行中...",
    forbidClick: true,
  });
    // console.log("api", api);
    // console.log("params", params);
  proxy
    .$post(api, params)
    .then((res) => {
      closeToast();
      currentLoadingOperation.value = false;
      if (res.code == 200) {
        showSuccessToast("操作成功");
        setTimeout(() => {
          getWindowList();
          getPublishlist();
        }, 1000);
      } else {
        showFailToast(res.msg);
      }
    })
    .catch((err) => {
      closeToast();
      currentLoadingOperation.value = false;
      showFailToast("操作失败");
      console.log(err);
    });
};

// 获取状态样式类
const getStatusClass = (status) => {
  if (status === '已发布') {
    return 'status-published';
  } else if (status === '未发布') {
    return 'status-unpublished';
  }
  return '';
};

// 跳转指定窗口
const goWindow = (item, mealtime_id) => {
  // console.log("跳转指定窗口信息 --->", item);
  router.push({
    name: "release_info",
    query: {
      date: formatDate(selectedDate.value),
      stall_id: item.stall_id,
      mealtime_id: mealtime_id,
      dininghall_id: localStorage.getItem('dininghall')
    },
  });
};
</script>

<style scoped lang="scss">
.content {
  padding: 16px;
  // background-color: #f5f5f5;
  min-height: 100vh;
}

.window-list {
  // background-color: #fff;
  border-radius: 12px;
  margin-top: 10px;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .repast-title {
    padding: 16px;
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    background-color: none;
    line-height: 20px;
    // background-color: #f8f9fa;
    // border-bottom: 1px solid #ebedf0;
  }

  .window-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-radius: 4px;
    height: 70px;
    background-color: #fff;
    border-bottom: 0.5px solid #ebedf0;
    // 设置border-bottom margin
    

    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f8f9fa;
    }

    &:active {
      background-color: #f0f0f0;
    }

    .window-content {
      flex: 1;

      .window-title {
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 4px;
      }

      .window-status {
        font-size: 14px;

        &.status-published {
          color: #07c160;
        }

        &.status-unpublished {
          color: #969799;
        }
      }
    }

    .arrow-icon {
      display: flex;
      align-items: center;
      justify-content: center;

      .arrow-right {
        width: 6px;
        height: 6px;
        border-top: 2px solid #c8c9cc;
        border-right: 2px solid #c8c9cc;
        transform: rotate(45deg);
        transition: border-color 0.2s;
      }
    }

    &:hover .arrow-icon .arrow-right {
      border-color: #969799;
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .content {
    padding: 12px;
  }

  .window-list {
    .repast-title {
      padding: 12px;
      font-size: 15px;
    }

    .window-item {
      padding: 12px;

      .window-content {
        .window-title {
          font-size: 15px;
        }

        .window-status {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
