<template>
  <div class="skeleton-demo-container">
    <!-- 列表组件演示 -->
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
      <!-- 新增按钮插槽 -->
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增包间</span>
          </div>
        </div>
      </template>
      <template #default="{ item }">
        <div class="demo-item" @click.stop="onCardClick(item)">
          <div class="card-image">
            <div v-if="item.image == ''" style=" width: 80px;
        height: 80px;
        border-radius: 8px;
        line-height: 80px;
        text-align: center;
        background-color: #007fff;
        color: #fff;
        font-size: 27px;">{{ item.title[0] }}</div>
            <van-image width="80" height="80" radius="8" :src="item.image" fit="cover" :show-loading="false" v-else />
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-desc">人数：{{ item.capacity }}</div>
            <div class="item-time">地址：{{ item.address }}</div>
            <van-tag plain type="primary" v-for="tag in item.tags.split(',')" :key="tag" class="dish-tag">{{ tag }}
            </van-tag>
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { showToast } from 'vant'
const router = useRouter();

// 骨架屏配置
const skeletonConfig = reactive({
  isShow: true,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
  curl: {
    ls: '/private_room/get_ls' // 留空，使用模拟数据
  },
  postData: {
    dininghall_id: localStorage.getItem('dininghall')
  },
  search: {
    isShow: true,
    isShowPopup: false
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    // 这里可以对数据进行格式化处理
    console.log('格式化数据:', data)
  },
  // 添加模拟数据标识
  mockData: true
})

const { proxy } = getCurrentInstance();
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '包间档案',
  });
};
setRight()
// 新增按钮点击事件
const onAddClick = () => {
  router.push('/roomConfig/roomManagement/add')
}
const onCardClick = (item) => {
  router.push({ path: "/roomConfig/roomManagement/detail", query: { id: item.id } });
};
</script>

<style lang="scss" scoped>
.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;

  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;

    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 16px;
  background: #fff;
  border-radius: 8px;

  .item-content {
    margin-left: 10px;
    margin-top: 12px;
    flex: 1;
    min-width: 0;

    /* 确保flex子项能够收缩 */
    .item-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: normal;
      color: #171A1D;
      margin-bottom: 8px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }



    .card-image {
      margin-right: 12px;
      flex-shrink: 0;

      .imgTitle {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        line-height: 80px;
        text-align: center;
        background-color: #007fff;
        color: #fff;
        font-size: 27px;
      }

      .van-image {
        border-radius: 8px;
        overflow: hidden;
      }
    }

    .dish-tag {
      margin-top: 10px;
      flex-shrink: 0;
      // background: #1989fa;
      // color: #fff;
      border: none;
      font-size: 12px;
      padding: 2px 8px;
      margin-right: 5px;
      // border-radius: 12px;
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
